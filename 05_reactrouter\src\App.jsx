import React from 'react'
import { <PERSON>, useNavigate } from 'react-router-dom'
import AppRouter from './routers/router'
export default function App () {
  const navigate = useNavigate()
  function navigateTo(path) {
    navigate(path)
  }
    return (
      <div className="app">
        <div className="header">
          <h1>Header</h1>
          <Link to="/home">Home</Link>
          <Link to="/about" replace>About</Link>
          <button onClick={()=>navigateTo('/order')}>订单</button>
          <span onClick={()=>navigateTo('/profile')}>我的</span>
        </div>
        <hr />
        <div className="content">
          <AppRouter />
        </div>
        <hr />
        <div className="footer">
        <h1>Footer</h1>
        </div>
        </div>
    )
  
}