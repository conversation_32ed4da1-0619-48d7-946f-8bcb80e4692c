import React, { PureComponent } from 'react'
import { <PERSON>pp<PERSON><PERSON><PERSON>, XxButton, XxWarnButton } from '../utils/style'; 

export class CssInJsText extends PureComponent {
  constructor(props){
    super(props);
    this.state={
      style:props.style
    }
  }
  render() {
    const {style} = this.state;
    return (
     <div>
      <AppWrapper style={style}>
        <div className="header">
         Header
        </div>
        <div className="content">
         Content
        </div>
        <div className="footer">
          This is a footer
        </div>
      </AppWrapper>
      <XxButton>
        green按钮
      </XxButton>
      <XxWarnButton>
        red按钮
      </XxWarnButton>
     </div>
    )
  }
}

export default CssInJsText