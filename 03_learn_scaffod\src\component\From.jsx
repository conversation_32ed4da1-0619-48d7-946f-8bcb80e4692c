import React, { PureComponent } from 'react'

export class From extends PureComponent {
  constructor(props){
    super(props);
       // 创建固定的水果选项列表
       this.fruitOptions = [
        {value: 'apple', text: '苹果'},
        {value: 'banana', text: '香蕉'},
        {value: 'orange', text: '橙子'},
        {value: 'grape', text: '葡萄'},
        {value: 'watermelon', text: '西瓜'}
      ];
    this.state = {
      username:'',
      password:'',
      isAgree:false,
      hobby:[
        {text:'篮球',value:'basketball',checked:false},
        {text:'足球',value:'football',checked:false},
        {text:'羽毛球',value:'badminton',checked:true},
      ],
       // 添加单选水果状态
       fruit: 'apple',
       // 添加多选水果状态
       fruits: ['banana', 'orange','watermelon','grape','apple']
    }
  }
  handleChange = (event) => {
   const name = event.target.name;
   const value = event.target.value;
   this.setState({
    [name]: value
   })
  }
  handleSubmit = (event) => {
    event.preventDefault(); // 阻止默认行为,阻止页面刷新,阻止默认提交行为,阻止默认跳转行为,阻止默认事件行为
    //拿到数据
    //发送网络请求
    event.preventDefault();
    console.log(this.state);
  }
  handleCheckboxChange = (event) => {
    // 在checkbox中,checked是布尔值,所以不需要像input那样获取value,它的value是on off
    const value = event.target.checked;
    this.setState({
      isAgree:value
    })
  }
  handleHobbyChange = (index) => (event) => {
   const hobby = [...this.state.hobby]; // 创建hobby数组的浅拷贝
   hobby[index].checked = event.target.checked; // 根据索引找到被点击的hobby项并修改其checked值
   this.setState({
    hobby // 更新state中的hobby数组，触发重新渲染
   })
  }
  handleMultiSelectChange = (event) => {
    // 使用Array.from将selectedOptions转换为数组并提取value值
    const selectedValues = Array.from(event.target.selectedOptions, option => option.value);
    
    this.setState({
      fruits: selectedValues
    });
  }
  render() {
    const { username, password,isAgree,hobby,fruits } = this.state;
    return (
      <div>
        <form onSubmit={this.handleSubmit}>
          {/* 受控组件必须要通过onChange事件来改变state的值,这时候交给react进行管理了,没有v-model这样的双向绑定,要自己手动实现 */}
           <label htmlFor="username">用户名:
           <input type="text" name="username" value={username} onChange={this.handleChange} id="username" />
           </label>
           <label htmlFor="password">密码:
          <input type="password" name="password" value={password} onChange={this.handleChange} id="password"  autoComplete="current-password"/>
          </label>
          <button>提交</button>
        </form>
        <div>
          用户协议:
          <input type="checkbox" checked={isAgree} onChange={this.handleCheckboxChange} />
        </div>
        <div>
          爱好:
          {
            hobby.map((item,index)=>(
              <div key={index}>
                <input type="checkbox" name="hobby" value={item.value} checked={item.checked} onChange={this.handleHobbyChange(index)} />
                {item.text}
              </div>
            ))
          }
        </div>
        <div>
            <label htmlFor="fruits">选择多个水果:
              <select 
                id="fruits" 
                multiple 
                value={fruits} 
                onChange={this.handleMultiSelectChange}
                style={{ height: '100px' }} // 设置高度以便看到多个选项
              >
                {
                  this.fruitOptions.map((item,index)=>(
                    <option key={index} value={item.value}>{item.text}</option>
                  ))
                }
              </select>
            </label>
            <p>您选择的水果是: {fruits.join(', ')}</p>
          </div>
      </div>
    )
  }
}

export default From 