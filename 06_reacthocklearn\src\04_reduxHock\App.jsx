import React, { memo } from 'react'
import {useSelector,useDispatch,shallowEqual} from 'react-redux'
import {incremented} from './Store/features/counter'
const App = memo(() => {
  // useSelector 获取store中的数据
  // useDispatch 获取dispatch函数
  // 就不用connect了,映射state和dispatch
  const counter = useSelector(state=>state.counter.count,shallowEqual) // 第二个参数是优化性能的,如果数据没有变化,则不重新渲染
  const dispatch = useDispatch()
  return (
   <div>
    <h1>Counter:{counter}</h1>
    <button onClick={()=>dispatch(incremented())}>Increment</button>
    <progress value={counter} max={100}></progress>
    
   </div>
  )
})

export default App