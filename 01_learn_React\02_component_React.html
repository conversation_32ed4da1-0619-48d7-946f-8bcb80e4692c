<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com; object-src 'none';">
  <title>Document</title>
</head>
<body>
  <script crossorigin="" src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin="" src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script type="text/babel">
    const root = ReactDOM.createRoot(document.querySelector('#root'));
    class Hello extends React.Component{
      constructor(props){
        super(props)
        this.state={
          message: 'Hello, React!',
          movies: ['The Dark Knight', 'The Dark Knight Rises', 'The Dark Knight']
        }
      
    }
    render(){
      return((
        <div>
          <h1>{this.state.message}</h1>
          <ul>
            {this.state.movies.map((movie, index) => {
              return <li key={index}>{movie}</li>
            })}
          </ul>
        </div>
      ))
    }
  }
    root.render((
      <div>
        <Hello />
      </div>
    ));
  </script>
  <div id="root"> </div>
</body>
</html>