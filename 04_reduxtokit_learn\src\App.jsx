import  { PureComponent } from 'react'
import { connect } from 'react-redux'
import Home from './pages/home';
import Profile from './pages/profile';
export class App extends PureComponent {
  render() {
    const {counter} = this.props;
    return (
        <div>
        <div>App:{counter} </div>
        <div className="pages">
          <Home />
          <Profile />
        </div>
      </div> 
    )
  }
}
const mapStateToProps = (state) => {
  return {
    counter: state.counter.value,
  }
  
}
export default connect(mapStateToProps)(App)
