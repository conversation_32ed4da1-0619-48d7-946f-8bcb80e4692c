<!-- JSX语法 -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>
<body>
  <script crossorigin="" src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin="" src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script type="text/babel">
    const root = ReactDOM.createRoot(document.querySelector('#root'));
    class Hello extends React.Component{
      constructor(props){ 
        super(props)
        this.state={
          message: 'Hello, React!',
          number: 100,
          array: ['The Dark Knight', 'The Dark Knight Rises', 'The Dark Knight'],
          object: {
            name: '<PERSON>',
            age: 30
          },
          aaa: true,
          bbb : null,
          ccc: undefined,
        }
      }
      render(){
        const {message, number, array, object, aaa, bbb, ccc} = this.state;
        return  
        {/* 使用JSX语法的注意事项 */}
        {/* 1. 使用JSX语法的时候只能有一个根节点,如果需要多个节点,需要使用一个div包裹 */}
        <div>
          <h1>{message}</h1> 
          <h1>{number}</h1>
          <h1>{array}</h1>
        {/* 2. 使用JSX语法的时候如果插入字符串,数组,数字能够正常直接显示,但是如果是对象类型,不能直接作为子元素显示,要确实到底显示什么元素,比如object.name这样确定下来 */}
        <h1>{object.name}</h1>
        {/* 3. 使用JSX语法的时候如果插入布尔值,null,undefined,函数,会忽略,如果想显示,那么转为字符串 */}
        <h1>{aaa.toString()}</h1>
          <h1>{bbb+""}</h1>
          <h1>{ccc+""}</h1>
        {/* 4. 使用JSX语法的时候可以插入对应的表达式*/}
        <h2>{20+50}</h2>
        <div></div>
      </div>
      }
    }
    root.render(<Hello />);
  </script>
  <div id="root"> </div>
</body>
</html>