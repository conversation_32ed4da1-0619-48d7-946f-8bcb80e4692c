import { createSlice,createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios'
const fetchData = createAsyncThunk('counter/fetchData',async()=>{
    const res = await axios.get('https://jsonplaceholder.typicode.com/posts')
    return res.data.slice(0,4)
})
const counterSlice = createSlice({
    name: 'counter',
    initialState: {
        value: 0,
        data:[]
    },
    reducers: {
        increment: (state,action) => {
            state.value += action.payload;
        },
        decrement: (state,action) => {
            state.value -= action.payload;
        },
    },
    extraReducers: (builder) => {
      builder.addCase(fetchData.fulfilled, (state, action) => {
          state.data = action.payload;
      })
  }
})
export const { increment, decrement} = counterSlice.actions;
export {fetchData}
export default counterSlice.reducer;