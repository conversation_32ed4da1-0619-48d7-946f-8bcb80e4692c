import { PureComponent } from "react";
import store from "../store";
export const   XXconnect = (mapStateToProps,mapDispatchToProps) => {
    return (Component) => {
        return class extends PureComponent{
            constructor(props){
                super(props);
                this.state = mapStateToProps(store.getState());
                this.unsubscribe = store.subscribe(()=>{
                    this.setState(mapStateToProps(store.getState()));
                })
            }
            componentWillUnmount(){
                this.unsubscribe();
            }
            render(){
                return <Component {...this.props} {...this.state} {...mapDispatchToProps(store.dispatch)} />
            }
        }

    }
}