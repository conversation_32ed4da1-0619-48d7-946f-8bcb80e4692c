import React  from 'react'
import MyContext from './CreateContext';

  export default function FunctionCompoent(props){
    
  
    return <MyContext.Consumer>
        {
            (value)=>{
                return ( 
                <div>
                <h3>函数式组件 - Consumer方式</h3>
                <p>从Context获取的消息: {value.message}</p>
                <p>组件类型: 函数式组件</p>
                </div>
                )
            }
        }
    </MyContext.Consumer>
}
