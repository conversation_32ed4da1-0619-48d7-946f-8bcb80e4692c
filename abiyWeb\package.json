{"name": "07_abiy", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@mui/material": "^7.2.0", "@mui/styled-engine-sc": "^7.2.0", "@reduxjs/toolkit": "^2.8.2", "antd": "^5.26.6", "axios": "^1.11.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "normalize.css": "^8.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.7.1", "styled-components": "^6.1.19"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "less": "^4.4.0", "vite": "^7.0.4"}}