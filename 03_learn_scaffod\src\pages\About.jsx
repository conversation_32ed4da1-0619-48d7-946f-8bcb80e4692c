import React, { PureComponent } from 'react'
import {connect} from 'react-redux';
import {addAction} from '../Store/actioncreator';
export class About extends PureComponent {
  render() {
    return  (
      <div>About:{this.props.counter}
        <button onClick={this.props.addCounter}>+1</button>
      </div>
    )
  }
}
const mapStateToProps = (state)=>({
  counter :state.counter
})
const mapDispatchToProps = (dispatch)=>({
  addCounter(){
    dispatch(addAction(1))
  }
})
export default connect(mapStateToProps,mapDispatchToProps)(About) 