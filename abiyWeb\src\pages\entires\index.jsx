import React, { memo } from 'react'
import {EntireWrapper} from './style'
import EntiresFilter from './d-cpns/entires-filter'
import EntiresContent from './d-cpns/entires-content'
import EntiresPagination from './d-cpns/entires-pagination'
const Entire = memo(() => {
  return (
    <EntireWrapper>
      <EntiresFilter />
      <EntiresContent />
      <EntiresPagination />
    </EntireWrapper>
  )
})

export default Entire