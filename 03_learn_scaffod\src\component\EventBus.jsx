import React, { Component } from 'react'
import eventBus from '../utils/event-bus'
export class EventBus extends Component {
  ClickPre(){
    eventBus.emit('pre','kebo',10,1.88); 
  }
  ClickNext(){
    eventBus.emit('next',10);
  }
  render() {
   
    return (
    
      <div>
        <button onClick={this.ClickPre}>上一个</button>
        <button onClick={this.ClickNext}>下一个</button>
      </div>
    )
  }
}

export default EventBus