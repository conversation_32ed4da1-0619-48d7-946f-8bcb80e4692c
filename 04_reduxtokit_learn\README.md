# 🚀 Redux Toolkit 计数器 Demo

这是一个使用 **Redux Toolkit** 实现的简单计数器应用，演示了现代化的 React 状态管理方式。

## ✨ 功能特性

- 🧮 **基础计数操作**: 增加、减少、重置
- ⚡ **快速操作**: 一键增减 5 或 10
- 🎛️ **步长设置**: 自定义每次操作的步长
- 📊 **状态监控**: 实时显示计数状态和统计信息
- 🎨 **美观界面**: 现代化的 UI 设计
- 🛠️ **开发工具**: 支持 Redux DevTools

## 🏗️ 技术栈

- **React 19.1.0** - 前端框架
- **Redux Toolkit 2.8.2** - 状态管理
- **React-Redux 9.2.0** - React 绑定
- **React Hooks** - 现代化的组件开发

## 📁 项目结构

```
src/
├── components/
│   ├── Counter.jsx          # 主计数器组件
│   └── CounterDisplay.jsx   # 状态显示组件
├── store/
│   ├── index.js            # Store 配置
│   └── counterSlice.js     # Counter Slice
├── App.jsx                 # 主应用组件
└── index.js               # 应用入口
```

## 🎯 Redux Toolkit 核心概念演示

### 1. **createSlice**
```javascript
const counterSlice = createSlice({
  name: 'counter',
  initialState: { value: 0, step: 1 },
  reducers: {
    increment: (state) => {
      state.value += state.step; // 使用 Immer 的"可变"语法
    },
    // ... 其他 reducers
  },
});
```

### 2. **configureStore**
```javascript
const store = configureStore({
  reducer: {
    counter: counterSlice,
  },
  devTools: process.env.NODE_ENV !== 'production',
});
```

### 3. **React Hooks 集成**
```javascript
const count = useSelector(selectCount);
const dispatch = useDispatch();
```

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm start
```

应用将在 [http://localhost:3000](http://localhost:3000) 打开。

### 构建生产版本
```bash
npm run build
```

## 🎮 使用说明

1. **基础操作**
   - 点击 ➕ 按钮增加计数
   - 点击 ➖ 按钮减少计数
   - 点击 🔄 按钮重置为 0

2. **快速操作**
   - 使用 +5, +10, -5, -10 按钮快速改变数值

3. **步长设置**
   - 选择预设步长 (1, 2, 5)
   - 或输入自定义步长值

4. **状态监控**
   - 右侧面板实时显示当前状态
   - 包含数值分析和统计信息

## 🛠️ 开发工具

### Redux DevTools
1. 安装 [Redux DevTools Extension](https://github.com/reduxjs/redux-devtools)
2. 打开浏览器开发者工具
3. 切换到 "Redux" 标签页
4. 实时查看 action 和 state 变化

## 📚 学习要点

这个 Demo 展示了以下 Redux Toolkit 的优势：

- ✅ **简化代码**: 相比传统 Redux 减少了大量样板代码
- ✅ **内置 Immer**: 支持"可变"语法的不可变更新
- ✅ **自动生成**: 自动创建 action creators 和 action types
- ✅ **开发体验**: 内置 Redux DevTools 和错误检查
- ✅ **TypeScript 友好**: 更好的类型推断和支持

## 🔗 相关资源

- [Redux Toolkit 官方文档](https://redux-toolkit.js.org/)
- [React-Redux Hooks API](https://react-redux.js.org/api/hooks)
- [Redux DevTools](https://github.com/reduxjs/redux-devtools)

### Code Splitting

This section has moved here: [https://facebook.github.io/create-react-app/docs/code-splitting](https://facebook.github.io/create-react-app/docs/code-splitting)

### Analyzing the Bundle Size

This section has moved here: [https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size](https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size)

### Making a Progressive Web App

This section has moved here: [https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app](https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app)

### Advanced Configuration

This section has moved here: [https://facebook.github.io/create-react-app/docs/advanced-configuration](https://facebook.github.io/create-react-app/docs/advanced-configuration)

### Deployment

This section has moved here: [https://facebook.github.io/create-react-app/docs/deployment](https://facebook.github.io/create-react-app/docs/deployment)

### `npm run build` fails to minify

This section has moved here: [https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify](https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify)
