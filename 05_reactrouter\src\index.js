import React from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import App from './App';
import { Suspense } from 'react';
const root = ReactDOM.createRoot(document.querySelector('#root'));
root.render(
    // 使用BrowserRouter包裹App组件,BrowserRouter是react-router-dom中的一个组件,用于创建一个浏览器路由器,App组件是我们的根组件,用于渲染我们的应用程序
    <BrowserRouter>
    {/* 使用Suspense包裹App组件,用于在App组件加载时显示一个加载中组件 异步操作*/}
          <Suspense fallback={<div>Loading...</div>}>
              <App />
          </Suspense>
    </BrowserRouter>
);



