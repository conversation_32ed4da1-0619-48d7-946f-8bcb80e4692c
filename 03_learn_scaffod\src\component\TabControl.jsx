import React, { Component } from 'react'
import './Tabcontrol.css';
export class TabControl extends Component {
  constructor(props){
    super(props);
    this.state={
      currentIndex:0
    }
  }
  handleClick(index){
    this.setState({
      currentIndex:index
    })
    this.props.changeTitle(index);
  }
  render() {
    const { tabs } = this.props;
    const { currentIndex } = this.state;
    return (
      <div className='tab-control'>
        {tabs.map((item,index)=>{
            return <span key={index} onClick={()=>this.handleClick(index)} className={currentIndex === index ? 'active' : ''}>{item}</span>
        })}
      </div>
    )
  }
}

export default TabControl