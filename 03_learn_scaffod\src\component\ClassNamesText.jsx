import React, { PureComponent } from 'react'
import classNames from 'classnames';
export class ClassNamesText extends PureComponent {
  constructor(props){
    super(props);
    this.state={
      isActive:true,
      isError:false
    }
  }
  render() {
    const {isActive,isError} = this.state;
    return (
      <div className={classNames({
        active:isActive,
        error:isError
      })}>
        <h1>ClassNamesText</h1>
        <button onClick={()=>this.setState({isActive:!isActive})}>切换</button>
        <button onClick={()=>this.setState({isError:!isError})}>切换</button>
      </div>
    )
  }
}

export default ClassNamesText