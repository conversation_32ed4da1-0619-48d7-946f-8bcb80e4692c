import React, { PureComponent } from 'react'
import { Routes, Route, Link, Navigate } from 'react-router-dom'
import Home from './pages/home'
import About from './pages/about'
import NotFound from './pages/notFound'
import HomeRecommend from './pages/homeRecommend'
import HomeRank from './pages/homeRank'
export class App extends PureComponent {
  render() {
    return (
      <div className="app">
        <div className="header">
          <h1>Header</h1>
          <Link to="/home">Home</Link>
          <Link to="/about" replace>About</Link>
        </div>
        <hr />
        <div className="content">
          <Routes>
            <Route path="/" element={<Navigate to="/home"/>} />
            <Route path="/home/<USER>" element={<Home />}>
              <Route path="recommend" element={<HomeRecommend />} />
              <Route path="rank" element={<HomeRank />} />
            </Route>
            <Route path="/about" element={<About />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </div>
        <hr />
        <div className="footer">
        <h1>Footer</h1>
        </div>
        </div>
    )
  }
}

export default App