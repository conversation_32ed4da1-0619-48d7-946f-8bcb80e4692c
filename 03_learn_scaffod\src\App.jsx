import React from 'react';
import PropTypes from 'prop-types';
import HelloWord from './component/HelloWord';
import Header from './component/Header';
import Main from './component/Main';
import Footer from './component/Footer';
import Decreamed from './component/Decreamed';
import Increamed from './component/Increamed';
import TabControl from './component/TabControl';
import SlotText from './component/SlotText';
import SlotTextProps from './component/SlotTextProps';
import ContextText from './component/ContextText';
import MyContext from './component/CreateContext';
import FunctionCompoent from './component/FunctionCompoent';
import EventBus from './component/EventBus';
import eventBus from './utils/event-bus';
import From from './component/From';
import HighOlderCnps from './component/HighOlderCnps';
import LessText from './component/LessText';
import CssInJsText from './component/CssInJsText';
import AntDesignDemo from './component/AntDesignDemo';
import ClassNamesText from './component/ClassNamesText';
import store from './Store';
import {addAction,subAction} from './Store/actioncreator';
import About from './pages/About';

class App extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      message: 'Hello React!',
      banner: ["banner1", "banner2", "banner3"],
      recommend: ["recommend1", "recommend2", "recommend3"],
      counter: 0,
      tabs:["推荐","最新","最热"],
      title:['推荐','最新','最热'],
      currentTitle:0,
      style:{
        backgroundColor:'red',  
        color:'#fff',
        fontSize:'20px'
      },
      counter_state :store.getState().counter
    };
    this.updateStoreState = this.updateStoreState.bind(this);
  }
  // render函数可以返回一个react元素,也就是通过react.createElement创建的元素的,那么可以是jsx对象,组件,还可以是数字,字符串,数组,null,undefined,布尔值后三者默认是不显示的
  //三个常见的声明周期 componentDidMount,componentDidUpdate,componentWillUnmount

  //组件之间的通信,父传子通过props,父亲传属性,子props接受,如果没有this.state那么不用写constructor,子传父通过回调函数
  subClick(count){
    this.setState({
     counter:this.state.counter-count
    });
  }
  addClick(count){
    this.setState({
     counter:this.state.counter+count
    });
  }
  changeTitle(index){
    this.setState({
      currentTitle:index
    })
  }
  componentDidMount(){ 
    eventBus.on('pre',(...args)=>{
      console.log('上一个',args);
    })
    eventBus.on('next',(...args)=>{
      console.log('下一个');
     })
     this.unsubscribe = store.subscribe(this.updateStoreState);
  }
  componentWillUnmount() {
    // 取消订阅，防止内存泄漏
    if (this.unsubscribe) {
      this.unsubscribe();
    }
  }
  addCounter_state(){
    store.dispatch(addAction(1));
    
  }
  subCounter_state(){
    store.dispatch(subAction(1));
  }
  updateStoreState(){
    this.setState({
      counter_state:store.getState().counter
    })
  }
    render() {
      const { message, banner, recommend, counter,tabs,title,currentTitle,style,counter_state } = this.state;
      return <div>
        <HelloWord />
        {message}
        <Header banner={banner} />
        <Main recommend={recommend} />
        <Footer /> 
        <h2>当前计数:{counter}</h2>
       <Decreamed subClick={(count)=>this.subClick(count)} />
       <Increamed addClick={(count)=>this.addClick(count)} />
       <TabControl tabs={tabs} changeTitle={(index)=>this.changeTitle(index)}/>
       <h2>{title[currentTitle]}</h2>
       {/* 通过children方式插槽传递组件,可以传递多个组件,也可以传递一个组件,当传递一个组件的时候是SlotText:button,当传递多个组件的时候会变成数组的形式,要记住索引不利于 
       第二种方式可以通过props传递组件*/}
       <SlotText >
        <button>left</button>
        <div>center</div>
        <i>斜体</i>
       </SlotText>
       <SlotTextProps left={<button>{title[currentTitle]}</button>} center={<div>center</div>} right={<i>斜体</i>} />
       {/* 组件中的通信使用context,首先我们需要引入createContext,然后创建一个context对象,在类组件中然后通过Provider组件传递数据,在函数式组件中然后通过Consumer组件接收数据 */}
       <MyContext.Provider value={{message}}>
       <ContextText />
       <FunctionCompoent/>
       </MyContext.Provider>
       <EventBus/>
       <From/>
       <HighOlderCnps/>
       <LessText/>
       <CssInJsText style={style}/>
       <AntDesignDemo/>
       <ClassNamesText/>
       <div>
        <h2>APP:当前计数:{counter_state}</h2>
        <button onClick={()=>this.addCounter_state()}>+1</button>
        <button onClick={()=>this.subCounter_state()}>-1</button>
       </div>
       <About/>
      </div>;
    }

}
App.propTypes = {
  message: PropTypes.string,
  banner: PropTypes.array,
  recommend: PropTypes.array,
};


export default App; 