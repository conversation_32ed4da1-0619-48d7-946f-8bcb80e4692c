<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com; object-src 'none';">
  <title>Document</title>
</head>
<body>
  <script crossorigin="" src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin="" src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script type="text/babel">
    let message = 'Hello, React!';
    const root = ReactDOM.createRoot(document.querySelector('#root'));
    renderAgain();
    function sayHello(){
      message = 'Hello, world!';
      renderAgain();
    }
    function renderAgain(){
      root.render((
      <div>
        <h1>{message}</h1>
        <button onClick={sayHello}>Click me</button>
      </div>
    ));
    } 
  </script>
  <div id="root"></div>
</body>
</html>