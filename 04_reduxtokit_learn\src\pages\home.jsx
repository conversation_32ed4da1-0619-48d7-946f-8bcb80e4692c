import React, { PureComponent } from 'react'
import { connect } from 'react-redux'
import { increment,fetchData } from '../features/counterSlice'
export class home extends PureComponent {
  increment(num){
    this.props.increment(num)
  }
  componentDidMount(){
    this.props.fetchData()
  }
  render() {
    const {counter} = this.props;
    return (
      <div>
        <div>home:{counter}</div>
        <button onClick={()=>this.increment(1)}>+1</button>
        <ul>
          {this.props.data.map((item)=>(
            <li key={item.id}>{item.title}</li>
          ))}
        </ul>
      </div>


    )
  }
}

const mapStateToProps = (state) => {
  return {
    counter: state.counter.value,
    data:state.counter.data,
  }
}
const mapDispatchToProps = (dispatch) => {
  return {
    increment: (num) => dispatch(increment(num)),
    fetchData: () => dispatch(fetchData()),
  }
}
export default connect(mapStateToProps,mapDispatchToProps)(home)