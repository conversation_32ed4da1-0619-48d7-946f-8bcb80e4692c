import { Routes, Route, Navigate } from "react-router-dom";
import Home from "../pages/home";
import About from "../pages/about";
import Order from "../pages/Order";
import Profile from "../pages/Profile";
import NotFound from "../pages/notFound";
// import HomeRecommend from "../pages/homeRecommend";
// import HomeRank from "../pages/homeRank";

import { lazy } from "react";
// const Home = lazy(() => import("../pages/home"));
// const About = lazy(() => import("../pages/about"));
// const Order = lazy(() => import("../pages/Order"));
// const Profile = lazy(() => import("../pages/Profile"));
// const NotFound = lazy(() => import("../pages/notFound"));

const HomeRecommend = lazy(() => import("../pages/homeRecommend"));// 分包,懒加载,按需加载
const HomeRank = lazy(() => import("../pages/homeRank"));
const AppRouter = () => {
 
  return (
    <Routes>
      <Route path="/" element={<Navigate to="/home"/>} />
      <Route path="/home/<USER>" element={<Home />}>
        <Route path="recommend" element={<HomeRecommend />} />
        <Route path="rank" element={<HomeRank />} />
      </Route>
      <Route path="/about" element={<About />} />
      <Route path="/order" element={<Order />} />
      <Route path="/profile" element={<Profile />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default AppRouter;