import React, { PureComponent } from 'react'
import { connect } from 'react-redux'
import { decrement } from '../features/counterSlice'

export class profile extends PureComponent {
  decrement(num){
    this.props.decrement(num)
  }
  render() {
    const {counter} = this.props;
    return (
      <div>
        <div>profile:{counter}</div>
        <button onClick={()=>this.decrement(1)}>-1</button>
      </div>
    )
  }
}
const mapStateToProps = (state) => {
  return {
    counter: state.counter.value,
  }
}
const mapDispatchToProps = (dispatch) => {
  return {
    decrement: (num) => dispatch(decrement(num)),
  }
}
export default connect(mapStateToProps,mapDispatchToProps)(profile)