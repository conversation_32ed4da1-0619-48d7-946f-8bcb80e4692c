<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>
<body>
  <div id="root"> </div>
  <script crossorigin="" src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin="" src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script type="text/babel">
    const root = ReactDOM.createRoot(document.querySelector('#root'));
    class Counter extends React.Component{
      constructor(props){
        super(props)
        this.state={
          count: 0
        }
      }
      increment(){
        this.setState({
          count: this.state.count + 1
        })
      }
      decrement(){
        this.setState({
          count: this.state.count - 1
        })
      }
      render(){
        return(
          <div>
            <h1>{this.state.count}</h1>
            <button onClick={this.increment.bind(this)}>Increment</button>
            <button onClick={this.decrement.bind(this)}>Decrement</button>
          </div>
        )
      }
    }
    root.render(<Counter />);
  </script> 
</body>
</html> 