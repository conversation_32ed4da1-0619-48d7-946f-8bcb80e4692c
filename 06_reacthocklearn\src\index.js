import React from 'react';
import ReactDOM from 'react-dom/client';
import { ThemeContext, UserContext } from './02_useContext/context';
import App from './04_reduxHock/App';
import {Provider} from 'react-redux'
import store from './04_reduxHock/Store'
const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
    <Provider store={store}>
       <ThemeContext.Provider value={{ color: "red" }}>
        <UserContext.Provider value={{ name: "张三", age: 18 }}>
            <App />
        </UserContext.Provider>
    </ThemeContext.Provider>
    </Provider>
);
