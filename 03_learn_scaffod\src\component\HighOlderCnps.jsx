import React, { PureComponent } from 'react'
import withTheme ,{ThemeContext} from '../context/with_theme';
export class HighOlderCnps extends PureComponent {
  render() {
    return (
      <div>
        <h3>高阶组件</h3>
        <p>主题颜色:
          <ThemeContext.Consumer>
            {value => <span>{value.theme}</span>}
          </ThemeContext.Consumer>
        </p>
      </div>
    )
  }
}

export default withTheme(HighOlderCnps);