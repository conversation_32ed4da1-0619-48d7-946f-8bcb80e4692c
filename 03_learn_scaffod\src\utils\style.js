import styled from "styled-components";

export const AppWrapper = styled.div`
    width: 100%;
    height: 100%;
    background-color: #f0f0f0;
    .header {
        background-color: ${props => props.style.backgroundColor};
        color: #fff;
        &:hover {
            background-color: skyblue;
            color: #000;
            font-size: 20px;
        }
    }
    .content {
        background-color: orange;
    }
    .footer {
        background-color: green;
        color: #fff;
    }
`;

export const XxButton = styled.button`
    border-radius: 15px;
    background-color: green;
    color: white;
    padding: 10px 20px;
    border: none;
    cursor: pointer;
    
    &:hover {
        background-color: darkgreen;
    }
`;

// 支持样式的继承
export const XxWarnButton = styled(XxButton)`
    background-color: red;
    
    &:hover {
        background-color: darkred;
    }
`;