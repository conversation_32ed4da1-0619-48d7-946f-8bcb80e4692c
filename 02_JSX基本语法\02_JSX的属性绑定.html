<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <title>Document</title>
</head>
<body> 
  <div id="root"> </div>
  <script crossorigin="" src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin="" src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

  <script type="text/babel">
    const root = ReactDOM.createRoot(document.querySelector('#root'));
    class Hello extends React.Component{
      constructor(props){
        super(props)
        this.state={
          message: 'Hello, React!',
          imgURL:'https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png',
          isActive:true,
          style:{
            color:'red',
            fontSize:'20px'
          },
          classList:['abc','cba'],
          count:0
        }
      }
      btn1Click(){
        this.setState({
          count:this.state.count+1
        })
      }
      btn2Click=()=>{
        this.setState({
          count:this.state.count-1
        })
      }
      btn3Click(){
        this.setState({
          count:6
        })
      }
      btn4Click(event,age,name){
       console.log(event,age,name);
      }
      render(){
        const {imgURL,isActive,style,classList} = this.state;
        if(isActive){
          classList.push('active'); 
        }


        return <div>
          {/* 基本属性绑定*/}
          <img src={imgURL} alt="地球" />
          <a href="https://www.baidu.com">百度</a>
          {/* 绑定class*/}
          <div className={classList.join(' ')}></div>
          {/* 绑定事件 三种this方式*/}
          <h2>计数器：{this.state.count}</h2>
          {/* 通过this的显示绑定方法 bind虽然也可以传递参数但是event事件放在最后一个参数了,所以不方便*/}
          <button onClick={this.btn1Click.bind(this)}>按钮一</button>
          {/* 类字段的方式,不常用*/}
          <button onClick={this.btn2Click}>按钮二</button>   
          {/* 箭头函数 常用的写法*/}
          <button onClick={()=>this.btn3Click()}>按钮三</button>
          {/* 事件对象 箭头函数传递参数非常方便*/}
          <button onClick={(event)=>this.btn4Click(event,18,'张三')}>按钮四</button>
        </div>
      }
    } 
    root.render(<Hello />);
  </script>
</body>
</html> 