import React, { memo, useEffect } from 'react'
import {DetailWrapper} from './style'
import { useSearchParams } from 'react-router-dom'

const Detail = memo(() => {
  const [searchParams] = useSearchParams()
  const roomId = searchParams.get('id')
  
  useEffect(() => {
    window.scrollTo(0,0)
  }, [])
  
  return (
    <DetailWrapper>
      <h1>房间详情页</h1>
      {roomId && <p>房间ID: {String(roomId)}</p>}
    </DetailWrapper>
  )
})

export default Detail