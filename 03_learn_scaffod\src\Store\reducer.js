import {ADD_ACTION,SUB_ACTION} from './constant';
const initialState = {
  counter:0,
  fridens:{
    name:'张三',
    age:18
  }
}

function reducer(state=initialState,action){
  switch(action.type){
      case ADD_ACTION:
          return {
              ...state,
              counter:state.counter+action.count
          }
      case SUB_ACTION:
          return {
              ...state, 
              counter:state.counter-action.count
          }
      default:
          return state;
  }
}
export default reducer;