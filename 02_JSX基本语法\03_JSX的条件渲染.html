<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>
<body>
  <div id="root"></div>
  <script crossorigin="" src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin="" src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

  <script type="text/babel">
    const root = ReactDOM.createRoot(document.querySelector('#root'));
    class App extends React.Component{
      constructor(props){
        super(props)
        this.state={
          isLogin:true,
          message:'Hello, React!',
          friends:{
            name:'张三',
            age:18,
            gender:'男'
          }
        }
      }
      render(){
        // 解构赋值
        const {isLogin,message,friends} = this.state;
        let content = null;
        // 条件判断
        if(isLogin){
            content = <h2>欢迎回来</h2>
          }else{
            content = <h2>请先登录</h2>
          }
        // 三元运算符
        const content2 = isLogin ? <h2>欢迎回来</h2> : <h2>请先登录</h2>;
        // 逻辑与运算符
        const content3 = friends&&friends.name + ' ' + friends.age + ' ' + friends.gender;
        return <div>
          <h1>App</h1>
          <div>
            {content}
          </div>
          <div>{isLogin ? <h2>欢迎回来</h2> : <h2>请先登录</h2>}</div>
          <div>{friends&&friends.name + ' ' + friends.age + ' ' + friends.gender}</div>
        </div>
      }
    }
    root.render(<App />);
  </script> 
</body>
</html>